-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建文章表
CREATE TABLE IF NOT EXISTS posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  user_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 插入示例数据
INSERT INTO users (name, email) VALUES 
  ('张三', 'zhang<PERSON>@example.com'),
  ('李四', '<EMAIL>');

INSERT INTO posts (title, content, user_id) VALUES 
  ('第一篇文章', '这是第一篇文章的内容', 1),
  ('第二篇文章', '这是第二篇文章的内容', 2);