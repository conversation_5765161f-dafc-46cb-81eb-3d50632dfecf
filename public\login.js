// API基础URL
const API_BASE = '/api';

// DOM元素
const loginTab = document.getElementById('loginTab');
const registerTab = document.getElementById('registerTab');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const loginFormElement = document.getElementById('loginFormElement');
const registerFormElement = document.getElementById('registerFormElement');

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示登录表单
    showLoginForm();
    
    // 绑定切换按钮事件
    loginTab.addEventListener('click', showLoginForm);
    registerTab.addEventListener('click', showRegisterForm);
    
    // 绑定表单提交事件
    loginFormElement.addEventListener('submit', handleLoginSubmit);
    registerFormElement.addEventListener('submit', handleRegisterSubmit);
});

// 显示登录表单
function showLoginForm() {
    // 切换按钮样式
    loginTab.classList.add('bg-white', 'text-gray-800');
    loginTab.classList.remove('text-white', 'hover:bg-white/10');
    
    registerTab.classList.remove('bg-white', 'text-gray-800');
    registerTab.classList.add('text-white', 'hover:bg-white/10');
    
    // 切换表单显示
    loginForm.classList.remove('hidden');
    registerForm.classList.add('hidden');
}

// 显示注册表单
function showRegisterForm() {
    // 切换按钮样式
    registerTab.classList.add('bg-white', 'text-gray-800');
    registerTab.classList.remove('text-white', 'hover:bg-white/10');
    
    loginTab.classList.remove('bg-white', 'text-gray-800');
    loginTab.classList.add('text-white', 'hover:bg-white/10');
    
    // 切换表单显示
    registerForm.classList.remove('hidden');
    loginForm.classList.add('hidden');
}

// 处理登录表单提交
async function handleLoginSubmit(e) {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = '登录中...';
    submitBtn.disabled = true;
    
    try {
        await handleLogin(username, password);
    } catch (error) {
        console.error('登录失败:', error);
    } finally {
        // 恢复按钮状态
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// 处理注册表单提交
async function handleRegisterSubmit(e) {
    e.preventDefault();
    
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('registerConfirmPassword').value;
    
    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = '注册中...';
    submitBtn.disabled = true;
    
    try {
        await handleRegister(username, email, password, confirmPassword);
    } catch (error) {
        console.error('注册失败:', error);
    } finally {
        // 恢复按钮状态
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// 登录功能
async function handleLogin(username, password) {
    try {
        if (!username || !password) {
            throw new Error('请填写完整的登录信息');
        }
        
        // 调用登录API
        const response = await fetch(`${API_BASE}/auth`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                username: username, 
                password: password 
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 登录成功，保存用户信息
            localStorage.setItem('user', JSON.stringify({
                ...data.data.user,
                loginTime: new Date().toISOString()
            }));
            
            showNotification('登录成功！正在跳转...', 'success');
            
            // 跳转到主页
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
            
        } else {
            throw new Error(data.error || '登录失败');
        }
        
    } catch (error) {
        showNotification(error.message, 'error');
        throw error;
    }
}

// 注册功能
async function handleRegister(username, email, password, confirmPassword) {
    try {
        // 验证输入
        if (!username || !email || !password || !confirmPassword) {
            throw new Error('请填写完整的注册信息');
        }
        
        if (password !== confirmPassword) {
            throw new Error('两次输入的密码不一致');
        }
        
        if (password.length < 6) {
            throw new Error('密码长度至少6位');
        }
        
        // 调用用户创建API
        const response = await fetch(`${API_BASE}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                name: username, 
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('注册成功！请登录', 'success');
            
            // 清空注册表单
            document.getElementById('registerFormElement').reset();
            
            // 切换到登录表单并填充用户名
            setTimeout(() => {
                showLoginForm();
                document.getElementById('loginUsername').value = username;
            }, 1500);
            
        } else {
            throw new Error(data.error || '注册失败');
        }
        
    } catch (error) {
        showNotification(error.message, 'error');
        throw error;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const icon = document.getElementById('notificationIcon');
    const title = document.getElementById('notificationTitle');
    const messageEl = document.getElementById('notificationMessage');
    
    // 设置图标和标题
    if (type === 'success') {
        icon.innerHTML = '✅';
        title.textContent = '成功';
        notification.querySelector('.bg-white').classList.add('border-l-4', 'border-green-500');
    } else if (type === 'error') {
        icon.innerHTML = '❌';
        title.textContent = '错误';
        notification.querySelector('.bg-white').classList.add('border-l-4', 'border-red-500');
    } else {
        icon.innerHTML = 'ℹ️';
        title.textContent = '提示';
        notification.querySelector('.bg-white').classList.add('border-l-4', 'border-blue-500');
    }
    
    messageEl.textContent = message;
    
    // 显示通知
    notification.classList.remove('hidden');
    notification.classList.add('animate-slide-up');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        notification.classList.add('hidden');
        notification.classList.remove('animate-slide-up');
        // 清除边框样式
        notification.querySelector('.bg-white').classList.remove('border-l-4', 'border-green-500', 'border-red-500', 'border-blue-500');
    }, 3000);
}
