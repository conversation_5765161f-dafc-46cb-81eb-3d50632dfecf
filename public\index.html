<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare 全栈应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .user-item, .post-item {
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .post-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .loading {
            text-align: center;
            color: #666;
        }
        
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .hello-world {
            text-align: center;
            font-size: 3rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 40px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Hello World 居中显示 -->
    <div class="hello-world">
        Hello World
    </div>

    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <div>
                    <h1>🚀 Cloudflare 全栈应用</h1>
                    <p>基于 Cloudflare Pages + Functions + D1 构建的示例应用</p>
                </div>
                <div>
                    <a href="auth.html" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;">
                        登录 / 注册
                    </a>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>👥 用户管理</h2>
            <form id="userForm">
                <div class="form-group">
                    <label for="userName">用户名：</label>
                    <input type="text" id="userName" required>
                </div>
                <div class="form-group">
                    <label for="userEmail">邮箱：</label>
                    <input type="email" id="userEmail" required>
                </div>
                <button type="submit">添加用户</button>
            </form>
            
            <h3>用户列表</h3>
            <div id="usersList" class="loading">加载中...</div>
        </div>
        
        <div class="section">
            <h2>📝 文章管理</h2>
            <form id="postForm">
                <div class="form-group">
                    <label for="postTitle">标题：</label>
                    <input type="text" id="postTitle" required>
                </div>
                <div class="form-group">
                    <label for="postContent">内容：</label>
                    <textarea id="postContent" required></textarea>
                </div>
                <div class="form-group">
                    <label for="postUserId">作者ID：</label>
                    <input type="number" id="postUserId" required>
                </div>
                <button type="submit">发布文章</button>
            </form>
            
            <h3>文章列表</h3>
            <div id="postsList" class="loading">加载中...</div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>