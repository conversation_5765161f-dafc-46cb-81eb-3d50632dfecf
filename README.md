# Cloudflare 全栈项目

基于 Cloudflare Pages + Functions + D1 构建的全栈应用示例。

## 项目结构

```
cloudflare-fullstack-app/
├── package.json              # 项目配置和依赖
├── wrangler.toml             # Wrangler 配置文件
├── migrations/               # D1 数据库迁移文件
│   └── 0001_initial.sql      # 初始数据库结构
├── public/                   # 静态文件目录
│   ├── index.html            # 主页面
│   ├── app.js                # 前端 JavaScript
│   └── functions/            # Cloudflare Functions
│       └── api/
│           ├── users.js      # 用户 API
│           └── posts.js      # 文章 API
└── .gitignore                # Git 忽略文件
```

## 功能特性

- 📊 **D1 数据库**: 用户和文章数据存储
- 🚀 **Cloudflare Functions**: RESTful API 端点
- 🎨 **响应式前端**: HTML/CSS/JavaScript 界面
- 🔗 **完整 CRUD**: 用户和文章的增删改查
- 🔐 **用户认证**: 登录注册系统
- 🎭 **Bento Grid 设计**: 现代化 Tailwind CSS 界面
- 📱 **移动端优化**: 响应式设计，支持各种设备

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 创建 D1 数据库
```bash
# 创建数据库
npx wrangler d1 create cloudflare-fullstack-db

# 复制返回的 database_id 到 wrangler.toml 文件中
```

### 3. 运行数据库迁移
```bash
# 本地开发环境
npm run db:local

# 生产环境
npm run db:migrate
```

### 4. 本地开发
```bash
npm run dev
```

### 5. 部署到生产环境
```bash
npm run deploy
```

## API 端点

### 用户管理
- `GET /api/users` - 获取所有用户
- `POST /api/users` - 创建新用户（支持密码）

### 用户认证
- `POST /api/auth` - 用户登录验证

### 文章管理
- `GET /api/posts` - 获取所有文章
- `POST /api/posts` - 创建新文章

## 数据库表结构

### users 表
- `id`: 主键 (自增)
- `name`: 用户名
- `email`: 邮箱 (唯一)
- `password`: 密码
- `created_at`: 创建时间

### posts 表
- `id`: 主键 (自增)
- `title`: 文章标题
- `content`: 文章内容
- `user_id`: 作者ID (外键)
- `created_at`: 创建时间

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+), Tailwind CSS
- **后端**: Cloudflare Functions
- **数据库**: Cloudflare D1 (SQLite)
- **部署**: Cloudflare Pages
- **工具**: Wrangler CLI
- **UI设计**: Bento Grid 布局, 玻璃态效果, 响应式设计

## 新增功能：登录注册系统

### 访问登录注册页面
访问 `http://localhost:8788/auth.html` 或部署后的 `/auth.html` 路径

### 功能特点
- 🎨 **Bento Grid 设计**: 采用现代化的卡片式布局
- 🌈 **动态背景**: 渐变色动画背景效果
- 💎 **玻璃态效果**: 半透明毛玻璃视觉效果
- 📱 **响应式设计**: 完美适配桌面端和移动端
- ⚡ **实时验证**: 表单输入实时验证和反馈
- 🔔 **通知系统**: 优雅的操作结果通知

### 使用方法

#### 注册新用户
1. 在注册卡片中填写用户名、邮箱和密码
2. 确认密码必须与密码一致
3. 点击"注册"按钮
4. 注册成功后会自动填充登录表单

#### 用户登录
1. 在登录卡片中输入用户名/邮箱和密码
2. 可选择"记住我"选项
3. 点击"登录"按钮
4. 登录成功后自动跳转到主页

#### 测试账户
- 用户名: `admin`
- 密码: `admin`

---

现在您可以开始使用这个带有现代化登录注册系统的 Cloudflare 全栈项目了！