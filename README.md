# Cloudflare 全栈项目

基于 Cloudflare Pages + Functions + D1 构建的全栈应用示例。

## 项目结构

```
cloudflare-fullstack-app/
├── package.json              # 项目配置和依赖
├── wrangler.toml             # Wrangler 配置文件
├── migrations/               # D1 数据库迁移文件
│   └── 0001_initial.sql      # 初始数据库结构
├── public/                   # 静态文件目录
│   ├── index.html            # 主页面
│   ├── app.js                # 前端 JavaScript
│   └── functions/            # Cloudflare Functions
│       └── api/
│           ├── users.js      # 用户 API
│           └── posts.js      # 文章 API
└── .gitignore                # Git 忽略文件
```

## 功能特性

- 📊 **D1 数据库**: 用户和文章数据存储
- 🚀 **Cloudflare Functions**: RESTful API 端点
- 🎨 **响应式前端**: HTML/CSS/JavaScript 界面
- 🔗 **完整 CRUD**: 用户和文章的增删改查

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 创建 D1 数据库
```bash
# 创建数据库
npx wrangler d1 create cloudflare-fullstack-db

# 复制返回的 database_id 到 wrangler.toml 文件中
```

### 3. 运行数据库迁移
```bash
# 本地开发环境
npm run db:local

# 生产环境
npm run db:migrate
```

### 4. 本地开发
```bash
npm run dev
```

### 5. 部署到生产环境
```bash
npm run deploy
```

## API 端点

### 用户管理
- `GET /api/users` - 获取所有用户
- `POST /api/users` - 创建新用户

### 文章管理
- `GET /api/posts` - 获取所有文章
- `POST /api/posts` - 创建新文章

## 数据库表结构

### users 表
- `id`: 主键 (自增)
- `name`: 用户名
- `email`: 邮箱 (唯一)
- `created_at`: 创建时间

### posts 表
- `id`: 主键 (自增)
- `title`: 文章标题
- `content`: 文章内容
- `user_id`: 作者ID (外键)
- `created_at`: 创建时间

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **后端**: Cloudflare Functions
- **数据库**: Cloudflare D1 (SQLite)
- **部署**: Cloudflare Pages
- **工具**: Wrangler CLI

---

现在您可以开始使用这个 Cloudflare 全栈项目模板了！