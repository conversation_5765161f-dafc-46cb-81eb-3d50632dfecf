// API 基础 URL
const API_BASE = '/api';

// DOM 元素
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');

// 工具函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg transform transition-all duration-300 translate-x-full`;
    
    // 根据类型设置样式
    switch (type) {
        case 'success':
            notification.className += ' bg-green-500 text-white';
            break;
        case 'error':
            notification.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            notification.className += ' bg-yellow-500 text-white';
            break;
        default:
            notification.className += ' bg-blue-500 text-white';
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2">${getIcon(type)}</span>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function getIcon(type) {
    switch (type) {
        case 'success':
            return '✅';
        case 'error':
            return '❌';
        case 'warning':
            return '⚠️';
        default:
            return 'ℹ️';
    }
}

function setLoading(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            处理中...
        `;
    } else {
        button.disabled = false;
    }
}

// 登录功能
async function handleLogin(username, password) {
    try {
        if (!username || !password) {
            throw new Error('请填写完整的登录信息');
        }

        // 调用登录API
        const response = await fetch(`${API_BASE}/auth`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (data.success) {
            // 登录成功，保存用户信息
            localStorage.setItem('user', JSON.stringify({
                ...data.data.user,
                loginTime: new Date().toISOString()
            }));

            showNotification('登录成功！正在跳转...', 'success');

            // 跳转到主页
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);

        } else {
            throw new Error(data.error || '登录失败');
        }

    } catch (error) {
        showNotification(error.message, 'error');
        throw error;
    }
}

// 注册功能
async function handleRegister(username, email, password, confirmPassword) {
    try {
        // 验证输入
        if (!username || !email || !password || !confirmPassword) {
            throw new Error('请填写完整的注册信息');
        }
        
        if (password !== confirmPassword) {
            throw new Error('两次输入的密码不一致');
        }
        
        if (password.length < 6) {
            throw new Error('密码长度至少为6位');
        }
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('请输入有效的邮箱地址');
        }
        
        // 调用用户创建API
        const response = await fetch(`${API_BASE}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: username,
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('注册成功！请使用新账户登录', 'success');
            
            // 清空注册表单
            registerForm.reset();
            
            // 自动填充登录表单
            document.getElementById('loginUsername').value = username;
            
        } else {
            throw new Error(data.error || '注册失败');
        }
        
    } catch (error) {
        showNotification(error.message, 'error');
        throw error;
    }
}

// 事件监听器
loginForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value.trim();
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    try {
        setLoading(submitButton, true);
        await handleLogin(username, password);
    } catch (error) {
        // 错误已在handleLogin中处理
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    }
});

registerForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value.trim();
    const confirmPassword = document.getElementById('confirmPassword').value.trim();
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    try {
        setLoading(submitButton, true);
        await handleRegister(username, email, password, confirmPassword);
    } catch (error) {
        // 错误已在handleRegister中处理
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    }
});

// 页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', () => {
    const user = localStorage.getItem('user');
    if (user) {
        const userData = JSON.parse(user);
        showNotification(`欢迎回来，${userData.username}！`, 'info');
        
        // 可以选择直接跳转到主页
        // setTimeout(() => {
        //     window.location.href = 'index.html';
        // }, 2000);
    }
});

// 添加输入框焦点效果
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.classList.add('scale-[1.02]');
    });
    
    input.addEventListener('blur', function() {
        this.parentElement.classList.remove('scale-[1.02]');
    });
});
