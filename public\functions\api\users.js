export async function onRequest(context) {
  const { request, env } = context;

  // 处理 GET 请求
  if (request.method === 'GET') {
    try {
      const result = await env.DB.prepare('SELECT * FROM users').all();

      return new Response(JSON.stringify({
        success: true,
        data: result.results
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: error.message
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }

  // 处理 POST 请求
  if (request.method === 'POST') {
    try {
      const { name, email, password } = await request.json();

    if (!name || !email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name and email are required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // 如果提供了密码，则包含密码字段
    let query, params;
    if (password) {
      query = 'INSERT INTO users (name, email, password) VALUES (?, ?, ?)';
      params = [name, email, password];
    } else {
      query = 'INSERT INTO users (name, email) VALUES (?, ?)';
      params = [name, email];
    }

    const result = await env.DB.prepare(query).bind(...params).run();

    return new Response(JSON.stringify({
      success: true,
      data: { id: result.meta.last_row_id, name, email }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  }

  // 处理 OPTIONS 请求 (CORS)
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    });
  }

  // 不支持的方法
  return new Response('Method not allowed', { status: 405 });
}