export async function onRequestGet(context) {
  const { env } = context;
  
  try {
    const result = await env.DB.prepare('SELECT * FROM users').all();
    
    return new Response(JSON.stringify({
      success: true,
      data: result.results
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

export async function onRequestPost(context) {
  const { request, env } = context;

  try {
    const { name, email, password } = await request.json();

    if (!name || !email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name and email are required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // 如果提供了密码，则包含密码字段
    let query, params;
    if (password) {
      query = 'INSERT INTO users (name, email, password) VALUES (?, ?, ?)';
      params = [name, email, password];
    } else {
      query = 'INSERT INTO users (name, email) VALUES (?, ?)';
      params = [name, email];
    }

    const result = await env.DB.prepare(query).bind(...params).run();

    return new Response(JSON.stringify({
      success: true,
      data: { id: result.meta.last_row_id, name, email }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}