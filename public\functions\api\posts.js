export async function onRequestGet(context) {
  const { env } = context;
  
  try {
    const result = await env.DB.prepare(`
      SELECT p.*, u.name as author_name 
      FROM posts p 
      JOIN users u ON p.user_id = u.id 
      ORDER BY p.created_at DESC
    `).all();
    
    return new Response(JSON.stringify({
      success: true,
      data: result.results
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

export async function onRequestPost(context) {
  const { request, env } = context;
  
  try {
    const { title, content, user_id } = await request.json();
    
    if (!title || !content || !user_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Title, content and user_id are required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
    
    const result = await env.DB.prepare(
      'INSERT INTO posts (title, content, user_id) VALUES (?, ?, ?)'
    ).bind(title, content, user_id).run();
    
    return new Response(JSON.stringify({
      success: true,
      data: { id: result.meta.last_row_id, title, content, user_id }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}