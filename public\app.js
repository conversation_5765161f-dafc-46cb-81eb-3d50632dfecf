// API 基础 URL
const API_BASE = '/api';

// DOM 元素
const userForm = document.getElementById('userForm');
const postForm = document.getElementById('postForm');
const usersList = document.getElementById('usersList');
const postsList = document.getElementById('postsList');

// 工具函数
function showError(container, message) {
    container.innerHTML = `<div class="error">错误: ${message}</div>`;
}

function showLoading(container) {
    container.innerHTML = '<div class="loading">加载中...</div>';
}

// 用户相关功能
async function loadUsers() {
    try {
        showLoading(usersList);
        const response = await fetch(`${API_BASE}/users`);
        const data = await response.json();
        
        if (data.success) {
            if (data.data.length === 0) {
                usersList.innerHTML = '<p>暂无用户</p>';
            } else {
                usersList.innerHTML = data.data.map(user => `
                    <div class="user-item">
                        <strong>ID: ${user.id}</strong> - ${user.name} (${user.email})
                        <br><small>创建时间: ${new Date(user.created_at).toLocaleString()}</small>
                    </div>
                `).join('');
            }
        } else {
            showError(usersList, data.error);
        }
    } catch (error) {
        showError(usersList, error.message);
    }
}

async function addUser(name, email) {
    try {
        const response = await fetch(`${API_BASE}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, email })
        });
        
        const data = await response.json();
        
        if (data.success) {
            userForm.reset();
            loadUsers();
            alert('用户添加成功！');
        } else {
            alert('添加用户失败: ' + data.error);
        }
    } catch (error) {
        alert('添加用户失败: ' + error.message);
    }
}

// 文章相关功能
async function loadPosts() {
    try {
        showLoading(postsList);
        const response = await fetch(`${API_BASE}/posts`);
        const data = await response.json();
        
        if (data.success) {
            if (data.data.length === 0) {
                postsList.innerHTML = '<p>暂无文章</p>';
            } else {
                postsList.innerHTML = data.data.map(post => `
                    <div class="post-item">
                        <h4>${post.title}</h4>
                        <div class="post-meta">
                            作者: ${post.author_name} | 创建时间: ${new Date(post.created_at).toLocaleString()}
                        </div>
                        <p>${post.content}</p>
                    </div>
                `).join('');
            }
        } else {
            showError(postsList, data.error);
        }
    } catch (error) {
        showError(postsList, error.message);
    }
}

async function addPost(title, content, userId) {
    try {
        const response = await fetch(`${API_BASE}/posts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ title, content, user_id: parseInt(userId) })
        });
        
        const data = await response.json();
        
        if (data.success) {
            postForm.reset();
            loadPosts();
            alert('文章发布成功！');
        } else {
            alert('发布文章失败: ' + data.error);
        }
    } catch (error) {
        alert('发布文章失败: ' + error.message);
    }
}

// 事件监听器
userForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const name = document.getElementById('userName').value.trim();
    const email = document.getElementById('userEmail').value.trim();
    
    if (name && email) {
        addUser(name, email);
    }
});

postForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const title = document.getElementById('postTitle').value.trim();
    const content = document.getElementById('postContent').value.trim();
    const userId = document.getElementById('postUserId').value.trim();
    
    if (title && content && userId) {
        addPost(title, content, userId);
    }
});

// 页面加载时初始化数据
document.addEventListener('DOMContentLoaded', () => {
    loadUsers();
    loadPosts();
});