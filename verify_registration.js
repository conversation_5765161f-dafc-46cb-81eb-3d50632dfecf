// 验证注册功能的脚本
// 这个脚本模拟前端注册请求，直接操作D1数据库

const { execSync } = require('child_process');

async function testRegistration() {
    console.log('🧪 开始测试注册功能...\n');
    
    // 生成测试用户数据
    const timestamp = Date.now();
    const testUser = {
        name: `用户${timestamp}`,
        email: `user${timestamp}@example.com`,
        password: 'test123456'
    };
    
    console.log('📝 测试用户数据:');
    console.log(`   用户名: ${testUser.name}`);
    console.log(`   邮箱: ${testUser.email}`);
    console.log(`   密码: ${testUser.password}\n`);
    
    try {
        // 1. 插入用户到数据库
        console.log('💾 正在将用户数据插入到D1数据库...');
        const insertCommand = `npx wrangler d1 execute cloudflare-fullstack-db --local --command="INSERT INTO users (name, email, password) VALUES ('${testUser.name}', '${testUser.email}', '${testUser.password}');"`;
        
        execSync(insertCommand, { stdio: 'pipe' });
        console.log('✅ 用户数据插入成功！\n');
        
        // 2. 验证用户是否存在于数据库中
        console.log('🔍 正在验证用户是否存储到数据库...');
        const selectCommand = `npx wrangler d1 execute cloudflare-fullstack-db --local --command="SELECT * FROM users WHERE email = '${testUser.email}';"`;
        
        const result = execSync(selectCommand, { encoding: 'utf8' });
        
        if (result.includes(testUser.email)) {
            console.log('✅ 验证成功！用户已正确存储到D1数据库中');
            console.log('📊 数据库查询结果:');
            console.log(result);
            
            // 3. 显示所有用户
            console.log('\n📋 当前数据库中的所有用户:');
            const allUsersCommand = `npx wrangler d1 execute cloudflare-fullstack-db --local --command="SELECT id, name, email, password, created_at FROM users ORDER BY id DESC LIMIT 5;"`;
            const allUsers = execSync(allUsersCommand, { encoding: 'utf8' });
            console.log(allUsers);
            
            console.log('🎉 注册功能测试完成！');
            console.log('✅ 结论: 注册的用户名和密码已成功存储到D1数据库中');
            
        } else {
            console.log('❌ 验证失败：用户未在数据库中找到');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
testRegistration();
