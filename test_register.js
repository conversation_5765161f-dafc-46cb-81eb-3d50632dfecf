// 测试注册功能的脚本
async function testRegister() {
    const testUser = {
        name: 'testuser' + Date.now(),
        email: 'test' + Date.now() + '@example.com',
        password: 'test123456'
    };
    
    console.log('测试注册用户:', testUser);
    
    try {
        const response = await fetch('http://localhost:8788/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testUser)
        });

        console.log('响应状态:', response.status);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));

        const responseText = await response.text();
        console.log('响应文本:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
            console.log('解析后的数据:', data);
        } catch (parseError) {
            console.log('❌ JSON解析失败:', parseError.message);
            console.log('原始响应:', responseText);
            return;
        }
        
        if (data.success) {
            console.log('✅ 注册成功！用户ID:', data.data.id);
            
            // 验证用户是否真的存储到数据库中
            const getUserResponse = await fetch('http://localhost:8788/api/users');
            const userData = await getUserResponse.json();
            
            if (userData.success) {
                const newUser = userData.data.find(user => user.id === data.data.id);
                if (newUser) {
                    console.log('✅ 用户已成功存储到数据库:', newUser);
                    console.log('用户名:', newUser.name);
                    console.log('邮箱:', newUser.email);
                    console.log('密码:', newUser.password);
                } else {
                    console.log('❌ 用户未在数据库中找到');
                }
            }
        } else {
            console.log('❌ 注册失败:', data.error);
        }
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 运行测试
testRegister();
