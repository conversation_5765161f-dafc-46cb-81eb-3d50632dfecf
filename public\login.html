<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .animated-bg {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-container {
            min-height: 500px;
            transition: all 0.3s ease-in-out;
        }
        
        @media (max-width: 768px) {
            .form-container {
                min-height: 400px;
            }
        }
    </style>
</head>
<body class="animated-bg min-h-screen flex items-center justify-center p-4">
    <!-- 浮动装饰元素 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-white opacity-5 rounded-full animate-float"></div>
        <div class="absolute top-3/4 right-1/4 w-48 h-48 bg-white opacity-5 rounded-full animate-float" style="animation-delay: -2s;"></div>
        <div class="absolute top-1/2 left-3/4 w-32 h-32 bg-white opacity-5 rounded-full animate-float" style="animation-delay: -4s;"></div>
    </div>

    <div class="w-full max-width-md mx-auto">
        <!-- 标题 -->
        <div class="text-center mb-8 animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                🚀 欢迎回来
            </h1>
            <p class="text-xl text-white/80">
                登录您的账户或创建新账户
            </p>
        </div>

        <!-- 切换按钮 -->
        <div class="flex mb-6 glass-effect rounded-lg p-1 animate-slide-up" style="animation-delay: 0.1s;">
            <button id="loginTab" class="flex-1 py-3 px-6 text-center font-semibold rounded-md transition-all duration-300 bg-white text-gray-800">
                登录
            </button>
            <button id="registerTab" class="flex-1 py-3 px-6 text-center font-semibold rounded-md transition-all duration-300 text-white hover:bg-white/10">
                注册
            </button>
        </div>

        <!-- 表单容器 -->
        <div class="glass-effect rounded-2xl p-8 form-container animate-slide-up" style="animation-delay: 0.2s;">
            <!-- 登录表单 -->
            <div id="loginForm" class="space-y-6">
                <h2 class="text-2xl font-bold text-white text-center mb-6">用户登录</h2>
                
                <form id="loginFormElement" class="space-y-4">
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">用户名或邮箱</label>
                        <input type="text" id="loginUsername" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请输入用户名或邮箱">
                    </div>
                    
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">密码</label>
                        <input type="password" id="loginPassword" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请输入密码">
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <label class="flex items-center text-white/80">
                            <input type="checkbox" class="mr-2 rounded">
                            <span class="text-sm">记住我</span>
                        </label>
                        <a href="#" class="text-sm text-white/80 hover:text-white transition-colors">忘记密码？</a>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-3 px-6 rounded-lg hover:from-purple-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        登录
                    </button>
                </form>
            </div>

            <!-- 注册表单 -->
            <div id="registerForm" class="space-y-6 hidden">
                <h2 class="text-2xl font-bold text-white text-center mb-6">用户注册</h2>
                
                <form id="registerFormElement" class="space-y-4">
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">用户名</label>
                        <input type="text" id="registerUsername" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请输入用户名">
                    </div>
                    
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">邮箱</label>
                        <input type="email" id="registerEmail" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请输入邮箱地址">
                    </div>
                    
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">密码</label>
                        <input type="password" id="registerPassword" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请输入密码">
                    </div>
                    
                    <div>
                        <label class="block text-white/90 text-sm font-medium mb-2">确认密码</label>
                        <input type="password" id="registerConfirmPassword" required 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
                               placeholder="请再次输入密码">
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-blue-500 to-teal-500 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-600 hover:to-teal-600 transform hover:scale-105 transition-all duration-300 shadow-lg">
                        注册
                    </button>
                </form>
            </div>
        </div>

        <!-- 返回首页链接 -->
        <div class="text-center mt-6 animate-slide-up" style="animation-delay: 0.3s;">
            <a href="index.html" class="text-white/80 hover:text-white transition-colors">
                ← 返回首页
            </a>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notificationIcon" class="mr-3"></div>
                <div>
                    <div id="notificationTitle" class="font-semibold"></div>
                    <div id="notificationMessage" class="text-sm text-gray-600"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
</body>
</html>
